/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "usart.h"
#include "gpio.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "e70_config.h"
#include <string.h>
#include <stdio.h>
#include "mlx90393.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// MLX90393相关变量
static MLX90393_Handle_t mlx_handle;
volatile uint8_t mlx90393_interrupt_flag = 0;  // MLX90393中断标志位

// 业务流程控制变量
static uint8_t e70_first_init_done = 0;  // E70首次初始化标志
static uint32_t e70_listen_start_time = 0;  // E70监听开始时间
static uint32_t e70_send_start_time = 0;    // E70发送开始时间
static uint8_t e70_send_retry_count = 0;    // E70发送重试次数
static uint16_t saved_battery_voltage = 0;  // 保存的电池电压值

// 业务流程状态枚举
typedef enum {
    STATE_WAKE_UP = 0,           // 唤醒阶段
    STATE_VOLTAGE_CHECK,         // 电压检测阶段
    STATE_E70_LISTEN,           // E70监听阶段
    STATE_E70_SEND_DATA,        // E70发送数据阶段
    STATE_E70_WAIT_ACK,         // E70等待确认阶段
    STATE_PREPARE_SLEEP         // 准备休眠阶段
} BusinessState_t;

static BusinessState_t current_state = STATE_WAKE_UP;

// E70通信测试变量
static uint32_t test_send_timer = 0;        // 发送测试定时器
static uint32_t test_receive_timer = 0;     // 接收测试定时器
static uint8_t test_mode = 1;               // 测试模式：1=发送端，0=接收端
static uint8_t test_running = 1;            // 测试运行标志
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
/* USER CODE BEGIN PFP */
void E70_TestCommunication(void);
void E70_SendTestData(void);
void E70_CheckTestReceive(void);
void E70_CheckUARTStatus(void);
void E70_LoopbackTest(void);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{

  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_I2C1_Init();
  MX_LPUART1_UART_Init();
  MX_USART2_UART_Init();
  MX_ADC_Init();
  /* USER CODE BEGIN 2 */
	printf("=== STM32L031G6 Low Power System ===\r\n");
	LED_ON;

	// E70首次初始化（生命周期内只执行一次）
	if (!e70_first_init_done) {
//		printf("=== E70 First Time Initialization ===\r\n");
		uint8_t init_result = E70_InitializeConfig(E70_MODULE_ADDRESS, 10, 1);
		if (!init_result) {
			printf("E70 Init FAILED!\r\n");
			Error_Handler();
		}
		E70_EnterCommMode();
//		printf("=== E70 Configuration Complete ===\r\n");
		e70_first_init_done = 1;  // 标记首次初始化完成
	}

	// 初始化时关闭所有模块电源
	MLX90393_PW_OFF;
	// RF_PWR_OFF;   // 保持通信模块开启用于测试

	// 设置测试模式（可以通过修改这个值来切换发送端/接收端）
	// test_mode = 1: 发送端（发送 FC FD 77 88 FD，期望收到 FC FD 55 66 FD）
	// test_mode = 0: 接收端（发送 FC FD 55 66 FD，期望收到 FC FD 77 88 FD）
	test_mode = 1;  // 默认为发送端，另一个设备设置为0

	printf("\r\n=== E70 Communication Test Ready ===\r\n");
	printf("Test Mode: %s\r\n", test_mode ? "SENDER" : "RECEIVER");

	// 执行回环测试
	printf("\r\n=== Starting Loopback Test ===\r\n");
	E70_LoopbackTest();
  /* USER CODE END 2 */

  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
    // E70通信测试主循环
    if (test_running) {
        E70_TestCommunication();
    }

    HAL_Delay(100);  // 主循环延时
  }
  /* USER CODE END 3 */
}

/**
  * @brief System Clock Configuration
  * @retval None
  */
void SystemClock_Config(void)
{
  RCC_OscInitTypeDef RCC_OscInitStruct = {0};
  RCC_ClkInitTypeDef RCC_ClkInitStruct = {0};
  RCC_PeriphCLKInitTypeDef PeriphClkInit = {0};

  /** Configure the main internal regulator output voltage
  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /** Initializes the RCC Oscillators according to the specified parameters
  * in the RCC_OscInitTypeDef structure.
  */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_MSI;
  RCC_OscInitStruct.MSIState = RCC_MSI_ON;
  RCC_OscInitStruct.MSICalibrationValue = 0;
  RCC_OscInitStruct.MSIClockRange = RCC_MSIRANGE_5;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_NONE;
  if (HAL_RCC_OscConfig(&RCC_OscInitStruct) != HAL_OK)
  {
    Error_Handler();
  }

  /** Initializes the CPU, AHB and APB buses clocks
  */
  RCC_ClkInitStruct.ClockType = RCC_CLOCKTYPE_HCLK|RCC_CLOCKTYPE_SYSCLK
                              |RCC_CLOCKTYPE_PCLK1|RCC_CLOCKTYPE_PCLK2;
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_MSI;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV1;
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV1;

  if (HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_0) != HAL_OK)
  {
    Error_Handler();
  }
  PeriphClkInit.PeriphClockSelection = RCC_PERIPHCLK_USART2|RCC_PERIPHCLK_LPUART1
                              |RCC_PERIPHCLK_I2C1;
  PeriphClkInit.Usart2ClockSelection = RCC_USART2CLKSOURCE_SYSCLK;
  PeriphClkInit.Lpuart1ClockSelection = RCC_LPUART1CLKSOURCE_PCLK1;
  PeriphClkInit.I2c1ClockSelection = RCC_I2C1CLKSOURCE_PCLK1;
  if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInit) != HAL_OK)
  {
    Error_Handler();
  }
}

/* USER CODE BEGIN 4 */

/**
 * @brief  E70通信测试主函数
 * @param  None
 * @retval None
 */
void E70_TestCommunication(void)
{
    static uint8_t test_initialized = 0;

    // 初始化测试
    if (!test_initialized) {
        printf("\r\n=== E70 Communication Test Start ===\r\n");
        if (test_mode) {
            printf("Test Mode: SENDER (Send FC FD 77 88 FD)\r\n");
        } else {
            printf("Test Mode: RECEIVER (Send FC FD 55 66 FD)\r\n");
        }

        // 启动连续接收
        E70_StartContinuousRx();
        test_send_timer = HAL_GetTick();
        test_receive_timer = HAL_GetTick();
        test_initialized = 1;
    }

    // 发送测试数据（每3秒发送一次）
    if ((HAL_GetTick() - test_send_timer) >= 3000) {
        E70_SendTestData();
        test_send_timer = HAL_GetTick();
    }

    // 检查接收数据（每500ms检查一次）
    if ((HAL_GetTick() - test_receive_timer) >= 500) {
        E70_CheckTestReceive();
        E70_CheckUARTStatus();
        test_receive_timer = HAL_GetTick();
    }
}

/**
 * @brief  发送测试数据
 * @param  None
 * @retval None
 */
void E70_SendTestData(void)
{
    uint8_t test_packet[5];

    // 构造测试数据包
    test_packet[0] = 0xFC;  // 固定头1
    test_packet[1] = 0xFD;  // 固定头2

    if (test_mode) {
        // 发送端：发送 FC FD 77 88 FD
        test_packet[2] = 0x77;
        test_packet[3] = 0x88;
        printf("Sending: FC FD 77 88 FD\r\n");
    } else {
        // 接收端：发送 FC FD 55 66 FD
        test_packet[2] = 0x55;
        test_packet[3] = 0x66;
        printf("Sending: FC FD 55 66 FD\r\n");
    }

    test_packet[4] = 0xFD;  // 结束符

    // 发送数据包
    HAL_StatusTypeDef status = HAL_UART_Transmit(&hlpuart1, test_packet, 5, 1000);
    if (status != HAL_OK) {
        printf("Send Failed!\r\n");
    }
}

/**
 * @brief  检查测试接收数据
 * @param  None
 * @retval None
 */
void E70_CheckTestReceive(void)
{
    extern volatile uint8_t uart_rx_buffer[];
    extern volatile uint16_t uart_rx_index;
    static uint32_t last_debug_time = 0;

    // 每5秒打印一次接收状态调试信息
    if ((HAL_GetTick() - last_debug_time) >= 5000) {
        printf("RX Debug: Buffer index=%d\r\n", uart_rx_index);
        if (uart_rx_index > 0) {
            printf("RX Data: ");
            for (int i = 0; i < uart_rx_index && i < 20; i++) {
                printf("%02X ", uart_rx_buffer[i]);
            }
            printf("\r\n");
        } else {
            printf("No data received yet\r\n");
        }
        last_debug_time = HAL_GetTick();
    }

    // 检查是否收到完整的5字节数据包
    if (uart_rx_index >= 5) {
        // 查找数据包起始位置
        for (int i = 0; i <= (uart_rx_index - 5); i++) {
            if (uart_rx_buffer[i] == 0xFC && uart_rx_buffer[i+1] == 0xFD && uart_rx_buffer[i+4] == 0xFD) {
                uint8_t data1 = uart_rx_buffer[i+2];
                uint8_t data2 = uart_rx_buffer[i+3];

                printf("Found packet at pos %d: FC FD %02X %02X FD\r\n", i, data1, data2);

                if (test_mode) {
                    // 发送端期望收到：FC FD 55 66 FD
                    if (data1 == 0x55 && data2 == 0x66) {
                        printf("*** RECEIVED EXPECTED: FC FD 55 66 FD ***\r\n");
                        printf("*** COMMUNICATION TEST PASSED! ***\r\n");
                    } else {
                        printf("Received: FC FD %02X %02X FD (Expected: 55 66)\r\n", data1, data2);
                    }
                } else {
                    // 接收端期望收到：FC FD 77 88 FD
                    if (data1 == 0x77 && data2 == 0x88) {
                        printf("*** RECEIVED EXPECTED: FC FD 77 88 FD ***\r\n");
                        printf("*** COMMUNICATION TEST PASSED! ***\r\n");
                    } else {
                        printf("Received: FC FD %02X %02X FD (Expected: 77 88)\r\n", data1, data2);
                    }
                }

                // 清空接收缓冲区
                E70_ClearRxBuffer();
                E70_StartContinuousRx();
                break;
            }
        }

        // 如果缓冲区满了但没找到有效数据包，清空缓冲区
        if (uart_rx_index >= 100) {
            printf("RX Buffer full, clearing...\r\n");
            E70_ClearRxBuffer();
            E70_StartContinuousRx();
        }
    }
}

/**
 * @brief  检查UART接收状态
 * @param  None
 * @retval None
 */
void E70_CheckUARTStatus(void)
{
    extern volatile uint32_t last_rx_tick;
    static uint32_t last_status_check = 0;

    // 每5秒检查一次UART状态（更频繁）
    if ((HAL_GetTick() - last_status_check) >= 5000) {
        printf("UART Status Check:\r\n");
        printf("- UART State: %d (Ready=32, Busy=34)\r\n", hlpuart1.gState);
        printf("- RX State: %d (Ready=32, Busy_RX=34)\r\n", hlpuart1.RxState);
        printf("- Last RX: %lu ms ago\r\n", HAL_GetTick() - last_rx_tick);

        // 检查UART是否还在接收状态 (HAL_UART_STATE_BUSY_RX = 0x22 = 34)
        if (hlpuart1.RxState != 0x22) {
            printf("UART RX not active (should be 34), restarting...\r\n");
            E70_StartContinuousRx();
        } else {
            printf("UART RX is active\r\n");
        }

        last_status_check = HAL_GetTick();
    }
}

/**
 * @brief  E70回环测试（测试自己发送自己接收）
 * @param  None
 * @retval None
 */
void E70_LoopbackTest(void)
{
    extern volatile uint8_t uart_rx_buffer[];
    extern volatile uint16_t uart_rx_index;

    printf("Loopback Test: Starting UART RX...\r\n");

    // 启动接收
    E70_StartContinuousRx();
    HAL_Delay(1000);  // 更长的延时确保接收启动

    // 检查UART状态
    printf("UART Status before send: gState=%d, RxState=%d\r\n", hlpuart1.gState, hlpuart1.RxState);

    printf("Sending test packet...\r\n");
    // 发送测试数据
    uint8_t test_data[] = {0xAA, 0xBB, 0xCC, 0xDD, 0xEE};
    HAL_StatusTypeDef tx_status = HAL_UART_Transmit(&hlpuart1, test_data, 5, 1000);
    printf("TX Status: %d\r\n", tx_status);

    // 等待接收
    printf("Waiting for data...\r\n");
    HAL_Delay(3000);  // 更长的等待时间

    printf("Loopback Result: Received %d bytes\r\n", uart_rx_index);
    if (uart_rx_index > 0) {
        printf("Data: ");
        for (int i = 0; i < uart_rx_index && i < 10; i++) {
            printf("%02X ", uart_rx_buffer[i]);
        }
        printf("\r\n");

        // 检查是否收到了发送的数据
        uint8_t found = 0;
        for (int i = 0; i <= (uart_rx_index - 5); i++) {
            if (uart_rx_buffer[i] == 0xAA && uart_rx_buffer[i+1] == 0xBB &&
                uart_rx_buffer[i+2] == 0xCC && uart_rx_buffer[i+3] == 0xDD &&
                uart_rx_buffer[i+4] == 0xEE) {
                printf("*** LOOPBACK TEST PASSED! ***\r\n");
                found = 1;
                break;
            }
        }
        if (!found) {
            printf("Loopback test failed - data mismatch\r\n");
        }
    } else {
        printf("*** LOOPBACK TEST FAILED - NO DATA RECEIVED ***\r\n");
        printf("This indicates UART RX or E70 module issue\r\n");
        printf("Final UART Status: gState=%d, RxState=%d\r\n", hlpuart1.gState, hlpuart1.RxState);
    }

    // 清空缓冲区准备正常测试
    E70_ClearRxBuffer();
    printf("=== Loopback Test Complete ===\r\n\r\n");
}

/* USER CODE END 4 */

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Error_Handler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}
#ifdef USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t *file, uint32_t line)
{
  /* USER CODE BEGIN 6 */
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */
  /* USER CODE END 6 */
}
#endif /* USE_FULL_ASSERT */
